name: Nightly Backup

on:
  schedule:
    - cron: "0 3 * * *" # Run at 3 AM UTC daily
  workflow_dispatch: # Allow manual triggering

# Prevent multiple backup jobs from running simultaneously
concurrency:
  group: backup
  cancel-in-progress: false

env:
  BACKUP_RETENTION_DAYS: 14
  find ./backups -name "sneezy-backup-*.tar.xz" -type f -mtime +"${BACKUP_RETENTION_DAYS}"

jobs:
  backup:
    runs-on: ubuntu-latest
    timeout-minutes: 30 # Prevent runaway jobs
    steps:
      - name: Create and commit nightly backup
        run: |
          # Create SSH directory and add server key
          mkdir -p ~/.ssh
          echo "${{ secrets.BACKUPS_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan ${{ vars.BACKUPS_SERVER_IP }} >> ~/.ssh/known_hosts

          # Create SSH config for simplified commands
          cat > ~/.ssh/config << EOF
          Host server
            HostName ${{ vars.BACKUPS_SERVER_IP }}
            User ${{ vars.BACKUPS_SSH_USERNAME }}
            IdentityFile ~/.ssh/id_rsa
          EOF

          TEMP_DIR="/tmp"
          FILENAME="sneezy-backup-$(date +'%Y%m%d-%H%M%S').tar"

          # Create backup script in place, with variables expanded
          cat > backup_script.sh << EOF
          #!/bin/bash
          set -eo pipefail

          echo "=== Starting backup process at $(date) ==="

          echo "Dumping databases..."
          docker exec -i sneezy-db mysqldump \
            --single-transaction \
            -h sneezy-db \
            -u ${{ secrets.BACKUPS_DB_USERNAME }} \
            -p${{ secrets.BACKUPS_DB_PASSWORD }} \
            --databases sneezy immortal > "${TEMP_DIR}"/dbdump.sql

          echo "Creating tar archive of game files..."
          # /home/<USER>
          # This is configured in the Dockerfile for the sneezy service.
          docker exec -i sneezy tar -c --exclude='core' -C /home/<USER>"${TEMP_DIR}/${FILENAME}"

          echo "Adding database dump to archive..."
          tar --owner=sneezy:1000 --group=sneezy:1000 -rf "${TEMP_DIR}/${FILENAME}" -C "${TEMP_DIR}" dbdump.sql

          echo "Compressing archive..."
          xz -f "${TEMP_DIR}/${FILENAME}"

          FILENAME="${FILENAME}.xz"

          echo "Moving archive to backups directory and updating symlink..."
          mv "${TEMP_DIR}/${FILENAME}" ${{ vars.BACKUPS_DIR }}/
          ln -sf "${{ vars.BACKUPS_DIR }}/${FILENAME}" "${{ vars.BACKUPS_DIR }}/latest.tar.xz"

          echo "Cleaning up temp files..."
          rm -f "${TEMP_DIR}/dbdump.sql"

          echo "=== Backup completed successfully at $(date) ==="
          EOF

          chmod +x backup_script.sh
          scp backup_script.sh server:${TEMP_DIR}/
          ssh server "bash /${TEMP_DIR}/backup_script.sh && rm /${TEMP_DIR}/backup_script.sh"
          scp server:${{ vars.BACKUPS_DIR }}/${FILENAME} .

          echo "Verifying backup integrity..."

          # Ensure backup file exists and is not empty
          if [ ! -s "${FILENAME}" ]; then
            echo "ERROR: Backup file is empty or does not exist!"
            exit 1
          fi

          # Verify the backup is a valid xz archive
          echo "Verifying backup integrity..."
          if ! xz -t "${FILENAME}"; then
            echo "ERROR: Backup file is not a valid xz archive!"
            exit 1
          fi

          # Check minimum expected size (1MB)
          if [ $(stat -c%s "${FILENAME}") -lt 1048576 ]; then
            echo "ERROR: Backup file is suspiciously small!"
            exit 1
          fi

          echo "Backup verification successful"

          git clone --depth=1 https://x-access-token:${{ secrets.BACKUPS_GITHUB_PAT }}@github.com/sneezymud/backups.git private-backups

          mkdir -p private-backups/backups
          mv "${FILENAME}" private-backups/backups/
          cd private-backups
          git add "./backups/${FILENAME}"

          echo "Removing backups older than "${BACKUP_RETENTION_DAYS}" days..."
          FILES_TO_REMOVE=$(find ./backups -name "sneezy-backup-*.tar.xz" -type f -mtime +"${BACKUP_RETENTION_DAYS}")
          REMOVED_COUNT=0

          for old_file in "${FILES_TO_REMOVE}"; do
            echo "Removing old backup: $(basename "${old_file}")"
            rm "${old_file}"
            git add "${old_file}"
            REMOVED_COUNT=$((REMOVED_COUNT + 1))
          done

          COMMIT_MSG="Backup for $(date +'%Y-%m-%d')"
          COMMIT_DETAILS="Added: ${FILENAME} ($(du -h ./backups/${FILENAME} | cut -f1))"

          if [ "${REMOVED_COUNT}" -gt 0 ]; then
            COMMIT_DETAILS="${COMMIT_DETAILS}\nRemoved: ${REMOVED_COUNT} backups older than ${BACKUP_RETENTION_DAYS} days"
          else
            COMMIT_DETAILS="${COMMIT_DETAILS}\nNo old backups to remove"
          fi

          COMMIT_DETAILS="${COMMIT_DETAILS}\nCreated by GitHub Actions workflow run: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          git commit -m "${COMMIT_MSG}" -m "${COMMIT_DETAILS}"
          git push

          echo "Nightly backup successfully created and committed to backups repository"
